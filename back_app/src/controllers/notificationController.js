const Notification = require('../models/Notification');
const User = require('../models/User');
const NotificationService = require('../services/notificationService');

// Add a new notification (Admin only)
exports.addNotification = async (req, res) => {
  try {
    const { title, description, sendImmediately } = req.body;
    const image = req.file ? req.file.path : null;

    if (!title || !description || !image) {
      return res.status(400).json({ message: 'Title, description, and image are required' });
    }

    const notification = new Notification({
      image,
      title,
      description
    });

    await notification.save();

    // If sendImmediately is true, mark as sent and send push notifications
    if (sendImmediately === 'true' || sendImmediately === true) {
      notification.isSent = true;
      notification.sentAt = new Date();
      await notification.save();

      // Send push notification to all users
      await sendNotificationToAllUsers(notification);
    }

    res.status(201).json({
      message: 'Notification added successfully',
      notification: {
        id: notification._id,
        image: notification.image,
        title: notification.title,
        description: notification.description,
        isSent: notification.isSent,
        sentAt: notification.sentAt,
        createdAt: notification.createdAt
      }
    });
  } catch (error) {
    console.error('Add notification error:', error.message);
    res.status(500).json({ message: 'Error adding notification', error: error.message });
  }
};

// Get all notifications (Public)
exports.getAllNotifications = async (req, res) => {
  try {
    const notifications = await Notification.find({}).sort({ createdAt: -1 });
    
    res.status(200).json({ 
      notifications: notifications.map(notification => ({
        id: notification._id,
        image: notification.image,
        title: notification.title,
        description: notification.description,
        isSent: notification.isSent,
        sentAt: notification.sentAt,
        createdAt: notification.createdAt
      }))
    });
  } catch (error) {
    console.error('Get notifications error:', error.message);
    res.status(500).json({ message: 'Error fetching notifications', error: error.message });
  }
};

// Mark notification as sent (Admin only)
exports.markAsSent = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findById(id);
    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    notification.isSent = true;
    notification.sentAt = new Date();
    await notification.save();

    // Send push notification to all users
    await sendNotificationToAllUsers(notification);

    res.status(200).json({
      message: 'Notification marked as sent successfully',
      notification: {
        id: notification._id,
        image: notification.image,
        title: notification.title,
        description: notification.description,
        isSent: notification.isSent,
        sentAt: notification.sentAt
      }
    });
  } catch (error) {
    console.error('Mark as sent error:', error.message);
    res.status(500).json({ message: 'Error marking notification as sent', error: error.message });
  }
};

// Delete a notification (Admin only)
exports.deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;
    
    const notification = await Notification.findByIdAndDelete(id);
    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    res.status(200).json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Delete notification error:', error.message);
    res.status(500).json({ message: 'Error deleting notification', error: error.message });
  }
};

// Helper function to send notification to all users
async function sendNotificationToAllUsers(notification) {
  try {
    console.log('📢 Sending notification to all users:', notification.title);

    // Get all users with active FCM tokens
    const users = await User.find({
      'fcmTokens.isActive': true,
      'notificationSettings.pushEnabled': { $ne: false }
    });

    console.log(`📱 Found ${users.length} users with active FCM tokens`);

    const notificationService = new NotificationService();
    let successCount = 0;
    let failureCount = 0;

    // Send notification to each user's active tokens
    for (const user of users) {
      const activeTokens = user.fcmTokens.filter(tokenObj => tokenObj.isActive);

      for (const tokenObj of activeTokens) {
        try {
          const result = await notificationService.sendNotificationToUser(
            tokenObj.token,
            notification.title,
            notification.description,
            {
              type: 'admin_notification',
              notificationId: notification._id.toString(),
              image: notification.image
            }
          );

          if (result.success) {
            successCount++;
          } else {
            failureCount++;

            // If token is invalid, mark it as inactive
            if (result.shouldRemoveToken) {
              tokenObj.isActive = false;
              await user.save();
              console.log(`🗑️ Marked invalid token as inactive for user ${user.firebaseUid}`);
            }
          }
        } catch (error) {
          console.error(`❌ Error sending notification to user ${user.firebaseUid}:`, error);
          failureCount++;
        }
      }
    }

    console.log(`✅ Notification sent: ${successCount} successful, ${failureCount} failed`);
  } catch (error) {
    console.error('❌ Error sending notification to all users:', error);
  }
}

// Debug: Get raw notification data (temporary)
exports.getRawNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const notification = await Notification.findById(id);

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    res.status(200).json({
      rawNotification: notification,
      hasImage: !!notification.image,
      hasDescription: !!notification.description
    });
  } catch (error) {
    console.error('Get raw notification error:', error.message);
    res.status(500).json({ message: 'Error fetching raw notification', error: error.message });
  }
};