const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Import models and services
const Notification = require('../src/models/Notification');
const User = require('../src/models/User');
const NotificationService = require('../src/services/notificationService');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

// Sample notification data
const sampleNotifications = [
  {
    title: "Welcome to ZeroKoin!",
    description: "Start your journey to earn Zero Koins by completing daily sessions.",
    image: "https://res.cloudinary.com/dw2ybyiek/image/upload/v1/notifications/welcome.png"
  },
  {
    title: "New Feature Alert!",
    description: "Check out our new calculator feature to track your earnings.",
    image: "https://res.cloudinary.com/dw2ybyiek/image/upload/v1/notifications/calculator.png"
  },
  {
    title: "Daily Reminder",
    description: "Don't forget to complete your daily sessions to maximize your earnings!",
    image: "https://res.cloudinary.com/dw2ybyiek/image/upload/v1/notifications/reminder.png"
  },
  {
    title: "Referral Bonus",
    description: "Invite friends and earn bonus Zero Koins for each successful referral.",
    image: "https://res.cloudinary.com/dw2ybyiek/image/upload/v1/notifications/referral.png"
  },
  {
    title: "System Maintenance",
    description: "Scheduled maintenance will occur tonight from 2-4 AM UTC. Thank you for your patience.",
    image: "https://res.cloudinary.com/dw2ybyiek/image/upload/v1/notifications/maintenance.png"
  }
];

// Helper function to create notification
async function createNotification(notificationData, sendImmediately = false) {
  try {
    console.log(`📝 Creating notification: ${notificationData.title}`);
    
    const notification = new Notification({
      image: notificationData.image,
      title: notificationData.title,
      description: notificationData.description,
      isSent: sendImmediately,
      sentAt: sendImmediately ? new Date() : null
    });

    await notification.save();
    console.log(`✅ Notification created with ID: ${notification._id}`);
    
    return notification;
  } catch (error) {
    console.error(`❌ Error creating notification:`, error);
    throw error;
  }
}

// Helper function to send notification to all users
async function sendNotificationToAllUsers(notification) {
  try {
    console.log('📢 Sending notification to all users:', notification.title);
    
    // Get all users with active FCM tokens
    const users = await User.find({
      'fcmTokens.isActive': true,
      'notificationSettings.pushEnabled': { $ne: false }
    });

    console.log(`📱 Found ${users.length} users with active FCM tokens`);

    if (users.length === 0) {
      console.log('⚠️ No users with active FCM tokens found');
      return;
    }

    const notificationService = new NotificationService();
    let successCount = 0;
    let failureCount = 0;

    // Send notification to each user's active tokens
    for (const user of users) {
      const activeTokens = user.fcmTokens.filter(tokenObj => tokenObj.isActive);
      
      for (const tokenObj of activeTokens) {
        try {
          const result = await notificationService.sendNotificationToUser(
            tokenObj.token,
            notification.title,
            notification.description,
            {
              type: 'admin_notification',
              notificationId: notification._id.toString(),
              image: notification.image
            }
          );

          if (result.success) {
            successCount++;
            console.log(`✅ Sent to user ${user.firebaseUid || user.email || 'unknown'}`);
          } else {
            failureCount++;
            console.log(`❌ Failed to send to user ${user.firebaseUid || user.email || 'unknown'}: ${result.error}`);
            
            // If token is invalid, mark it as inactive
            if (result.shouldRemoveToken) {
              tokenObj.isActive = false;
              await user.save();
              console.log(`🗑️ Marked invalid token as inactive for user ${user.firebaseUid}`);
            }
          }
        } catch (error) {
          console.error(`❌ Error sending notification to user ${user.firebaseUid}:`, error);
          failureCount++;
        }
      }
    }

    console.log(`📊 Notification sending complete: ${successCount} successful, ${failureCount} failed`);
  } catch (error) {
    console.error('❌ Error sending notification to all users:', error);
  }
}

// Main script functions
async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function createSampleNotification(index = 0, sendImmediately = false) {
  if (index >= sampleNotifications.length) {
    console.log('❌ Invalid notification index. Available indices: 0-' + (sampleNotifications.length - 1));
    return;
  }

  const notificationData = sampleNotifications[index];
  const notification = await createNotification(notificationData, sendImmediately);
  
  if (sendImmediately) {
    await sendNotificationToAllUsers(notification);
  }
  
  return notification;
}

async function sendExistingNotification(notificationId) {
  try {
    const notification = await Notification.findById(notificationId);
    if (!notification) {
      console.log('❌ Notification not found');
      return;
    }

    if (notification.isSent) {
      console.log('⚠️ Notification already sent');
      return;
    }

    // Mark as sent
    notification.isSent = true;
    notification.sentAt = new Date();
    await notification.save();

    // Send to all users
    await sendNotificationToAllUsers(notification);
    
    console.log('✅ Notification sent successfully');
  } catch (error) {
    console.error('❌ Error sending existing notification:', error);
  }
}

async function listNotifications() {
  try {
    const notifications = await Notification.find({}).sort({ createdAt: -1 });
    
    console.log('\n📋 All Notifications:');
    console.log('='.repeat(80));
    
    if (notifications.length === 0) {
      console.log('No notifications found');
      return;
    }

    notifications.forEach((notification, index) => {
      const status = notification.isSent ? '✅ SENT' : '⏳ PENDING';
      const sentAt = notification.sentAt ? ` (${notification.sentAt.toLocaleString()})` : '';
      
      console.log(`${index + 1}. [${notification._id}] ${status}${sentAt}`);
      console.log(`   Title: ${notification.title}`);
      console.log(`   Description: ${notification.description}`);
      console.log(`   Created: ${notification.createdAt.toLocaleString()}`);
      console.log('-'.repeat(80));
    });
  } catch (error) {
    console.error('❌ Error listing notifications:', error);
  }
}

async function showHelp() {
  console.log('\n🚀 ZeroKoin Notification Script');
  console.log('='.repeat(50));
  console.log('Usage: node send_notification.js [command] [options]');
  console.log('\nCommands:');
  console.log('  create [index] [--send]  - Create sample notification (index 0-4)');
  console.log('  send [notificationId]    - Send existing notification');
  console.log('  list                     - List all notifications');
  console.log('  samples                  - Show sample notifications');
  console.log('  help                     - Show this help');
  console.log('\nExamples:');
  console.log('  node send_notification.js create 0        # Create notification (don\'t send)');
  console.log('  node send_notification.js create 1 --send # Create and send immediately');
  console.log('  node send_notification.js send 507f1f77bcf86cd799439011');
  console.log('  node send_notification.js list');
}

async function showSamples() {
  console.log('\n📝 Sample Notifications:');
  console.log('='.repeat(50));
  
  sampleNotifications.forEach((notification, index) => {
    console.log(`${index}. ${notification.title}`);
    console.log(`   ${notification.description}`);
    console.log(`   Image: ${notification.image}`);
    console.log('-'.repeat(50));
  });
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command || command === 'help') {
    await showHelp();
    return;
  }

  await connectToDatabase();

  try {
    switch (command) {
      case 'create':
        const index = parseInt(args[1]) || 0;
        const sendImmediately = args.includes('--send');
        await createSampleNotification(index, sendImmediately);
        break;

      case 'send':
        const notificationId = args[1];
        if (!notificationId) {
          console.log('❌ Please provide notification ID');
          break;
        }
        await sendExistingNotification(notificationId);
        break;

      case 'list':
        await listNotifications();
        break;

      case 'samples':
        await showSamples();
        break;

      default:
        console.log('❌ Unknown command. Use "help" to see available commands.');
    }
  } catch (error) {
    console.error('❌ Script error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('👋 Database connection closed');
  }
}

// Run the script
main().catch(console.error);
