const mongoose = require('mongoose');
const Notification = require('../src/models/Notification');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

async function clearTestNotifications() {
  try {
    // Connect to database
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all test notifications
    console.log('🔍 Finding test notifications...');
    const testNotifications = await Notification.find({
      title: "🎉 Test Notification"
    });

    console.log(`📝 Found ${testNotifications.length} test notifications`);

    if (testNotifications.length === 0) {
      console.log('✅ No test notifications found to clear');
      return;
    }

    // Delete all test notifications
    console.log('🗑️ Deleting test notifications...');
    const deleteResult = await Notification.deleteMany({
      title: "🎉 Test Notification"
    });

    console.log(`✅ Successfully deleted ${deleteResult.deletedCount} test notifications`);
    
    // Also clear any other test notifications that might have different titles
    console.log('🔍 Checking for other test notifications...');
    const otherTestNotifications = await Notification.find({
      $or: [
        { title: { $regex: /test/i } },
        { description: { $regex: /test.*script/i } }
      ]
    });

    if (otherTestNotifications.length > 0) {
      console.log(`📝 Found ${otherTestNotifications.length} other test notifications`);
      console.log('Titles found:');
      otherTestNotifications.forEach(notif => {
        console.log(`  - "${notif.title}"`);
      });

      console.log('🗑️ Deleting other test notifications...');
      const otherDeleteResult = await Notification.deleteMany({
        $or: [
          { title: { $regex: /test/i } },
          { description: { $regex: /test.*script/i } }
        ]
      });

      console.log(`✅ Successfully deleted ${otherDeleteResult.deletedCount} other test notifications`);
    }

    console.log('\n🎉 All test notifications have been cleared!');
    console.log('💡 The notification page should now be clean.');

  } catch (error) {
    console.error('❌ Failed to clear test notifications:', error);
  } finally {
    await mongoose.connection.close();
    console.log('👋 Database connection closed');
  }
}

// Run the cleanup
console.log('🧹 Starting Test Notification Cleanup...');
console.log('='.repeat(50));
clearTestNotifications().catch(console.error);
