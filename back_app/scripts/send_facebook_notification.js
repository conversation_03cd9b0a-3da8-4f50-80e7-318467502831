const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Import models
const Notification = require('../src/models/Notification');
const User = require('../src/models/User');
const NotificationService = require('../src/services/notificationService');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Helper function to send notification to all users
async function sendNotificationToAllUsers(notification) {
  try {
    console.log('📢 Sending notification to all users:', notification.title);

    // Get all users with active FCM tokens
    const users = await User.find({
      'fcmTokens.isActive': true,
      'notificationSettings.pushEnabled': { $ne: false }
    });

    console.log(`📱 Found ${users.length} users with active FCM tokens`);

    const notificationService = new NotificationService();
    let successCount = 0;
    let failureCount = 0;

    // Send notification to each user's active tokens
    for (const user of users) {
      const activeTokens = user.fcmTokens.filter(tokenObj => tokenObj.isActive);

      for (const tokenObj of activeTokens) {
        try {
          const result = await notificationService.sendNotificationToUser(
            tokenObj.token,
            notification.title,
            notification.description,
            {
              type: 'admin_notification',
              notificationId: notification._id.toString(),
              image: notification.image
            }
          );

          if (result.success) {
            successCount++;
          } else {
            failureCount++;

            // If token is invalid, mark it as inactive
            if (result.shouldRemoveToken) {
              tokenObj.isActive = false;
              await user.save();
              console.log(`🗑️ Marked invalid token as inactive for user ${user.firebaseUid}`);
            }
          }
        } catch (error) {
          console.error(`❌ Error sending notification to user ${user.firebaseUid}:`, error);
          failureCount++;
        }
      }
    }

    console.log(`✅ Notification sent: ${successCount} successful, ${failureCount} failed`);
  } catch (error) {
    console.error('❌ Error sending notification to all users:', error);
  }
}

// Main function to create and send Facebook notification
async function sendFacebookNotification() {
  try {
    await connectDB();

    console.log('📝 Creating Facebook notification...');

    // Create the notification
    const notification = new Notification({
      image: 'assets/Facebook.svg', // Using the Facebook icon from assets
      title: 'new post',
      description: 'check our new video on facebook',
      isSent: true,
      sentAt: new Date()
    });

    await notification.save();
    console.log(`✅ Notification created with ID: ${notification._id}`);

    // Send push notification to all users
    await sendNotificationToAllUsers(notification);

    console.log('🎉 Facebook notification sent successfully!');
    
    // Display notification details
    console.log('\n📋 Notification Details:');
    console.log(`Title: ${notification.title}`);
    console.log(`Description: ${notification.description}`);
    console.log(`Image: ${notification.image}`);
    console.log(`Created At: ${notification.createdAt}`);
    console.log(`Sent At: ${notification.sentAt}`);

  } catch (error) {
    console.error('❌ Error sending Facebook notification:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the script
sendFacebookNotification();
