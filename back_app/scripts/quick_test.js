const mongoose = require('mongoose');
const Notification = require('../src/models/Notification');
const User = require('../src/models/User');
const NotificationService = require('../src/services/notificationService');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

async function quickTest() {
  try {
    // Connect to database
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create a test notification
    console.log('📝 Creating test notification...');
    const testNotification = new Notification({
      title: "🎉 Test Notification",
      description: "This is a test notification sent from the script. If you receive this, the integration is working!",
      image: "https://res.cloudinary.com/dw2ybyiek/image/upload/v1/notifications/test.png",
      isSent: true,
      sentAt: new Date()
    });

    await testNotification.save();
    console.log(`✅ Test notification created with ID: ${testNotification._id}`);

    // Get users with FCM tokens
    console.log('👥 Finding users with FCM tokens...');
    const users = await User.find({
      'fcmTokens.isActive': true,
      'notificationSettings.pushEnabled': { $ne: false }
    });

    console.log(`📱 Found ${users.length} users with active FCM tokens`);

    if (users.length === 0) {
      console.log('⚠️ No users with active FCM tokens found. Make sure at least one user is logged in to the app.');
      return;
    }

    // Send notification to all users
    console.log('📢 Sending test notification to all users...');
    const notificationService = new NotificationService();
    let successCount = 0;
    let failureCount = 0;

    for (const user of users) {
      const activeTokens = user.fcmTokens.filter(tokenObj => tokenObj.isActive);
      console.log(`📤 Sending to user ${user.email || user.firebaseUid || 'unknown'} (${activeTokens.length} tokens)`);
      
      for (const tokenObj of activeTokens) {
        try {
          const result = await notificationService.sendNotificationToUser(
            tokenObj.token,
            testNotification.title,
            testNotification.description,
            {
              type: 'admin_notification',
              notificationId: testNotification._id.toString(),
              image: testNotification.image
            }
          );

          if (result.success) {
            successCount++;
            console.log(`  ✅ Success`);
          } else {
            failureCount++;
            console.log(`  ❌ Failed: ${result.error}`);
            
            if (result.shouldRemoveToken) {
              tokenObj.isActive = false;
              await user.save();
              console.log(`  🗑️ Marked invalid token as inactive`);
            }
          }
        } catch (error) {
          console.error(`  ❌ Error:`, error.message);
          failureCount++;
        }
      }
    }

    console.log('\n📊 Test Results:');
    console.log(`✅ Successful sends: ${successCount}`);
    console.log(`❌ Failed sends: ${failureCount}`);
    console.log(`📝 Notification ID: ${testNotification._id}`);
    
    if (successCount > 0) {
      console.log('\n🎉 SUCCESS! Check your app for the test notification.');
      console.log('💡 The notification should appear as a push notification and in the notification page.');
    } else {
      console.log('\n⚠️ No notifications were sent successfully.');
      console.log('💡 Make sure:');
      console.log('   - At least one user is logged into the app');
      console.log('   - The app has notification permissions enabled');
      console.log('   - Firebase configuration is correct');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('👋 Database connection closed');
  }
}

// Run the test
console.log('🚀 Starting ZeroKoin Notification Quick Test...');
console.log('='.repeat(50));
quickTest().catch(console.error);
