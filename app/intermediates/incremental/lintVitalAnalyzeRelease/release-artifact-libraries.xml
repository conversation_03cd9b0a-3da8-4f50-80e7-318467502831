<libraries>
  <library
      name="__local_aars__:/Users/<USER>/Desktop/zero_koin/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
      jars="/Users/<USER>/Desktop/zero_koin/build/app/intermediates/flutter/release/libs.jar"
      resolved="__local_aars__:/Users/<USER>/Desktop/zero_koin/build/app/intermediates/flutter/release/libs.jar:unspecified"/>
  <library
      name="com.google.firebase:firebase-auth:23.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/b4b619f8f5850c3998ed438d4fba7a35/transformed/jetified-firebase-auth-23.2.1/jars/classes.jar"
      resolved="com.google.firebase:firebase-auth:23.2.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/b4b619f8f5850c3998ed438d4fba7a35/transformed/jetified-firebase-auth-23.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:shared_preferences_android::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c79ff8720d56b6596417d359895fbe6a/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/c79ff8720d56b6596417d359895fbe6a/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.sharedpreferences:shared_preferences_android:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/shared_preferences_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c79ff8720d56b6596417d359895fbe6a/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/b909b13fd4fc31ca1f32e11211a13a82/transformed/jetified-credentials-play-services-auth-1.2.0-rc01/jars/classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/b909b13fd4fc31ca1f32e11211a13a82/transformed/jetified-credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c19aa9c13ad212a1596d1527a72c832d/transformed/jetified-credentials-1.2.0-rc01/jars/classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c19aa9c13ad212a1596d1527a72c832d/transformed/jetified-credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/916f7288390664cdfb194145196f6e10/transformed/jetified-play-services-auth-api-phone-18.0.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/916f7288390664cdfb194145196f6e10/transformed/jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/18aef82fa04be0c061bd99554cc384ad/transformed/jetified-recaptcha-18.6.1/jars/classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.6.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/18aef82fa04be0c061bd99554cc384ad/transformed/jetified-recaptcha-18.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/53b1f15807001c135c7e1507b077035c/transformed/jetified-integrity-1.3.0/jars/classes.jar"
      resolved="com.google.android.play:integrity:1.3.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/53b1f15807001c135c7e1507b077035c/transformed/jetified-integrity-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/d83172fadf2548efe1e88d3ac66cae7b/transformed/jetified-firebase-appcheck-interop-17.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/d83172fadf2548efe1e88d3ac66cae7b/transformed/jetified-firebase-appcheck-interop-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0fb851f9121c81afbee4b8083cd62263/transformed/jetified-firebase-auth-interop-20.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0fb851f9121c81afbee4b8083cd62263/transformed/jetified-firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/308745a438449d9b48372a87eb24c78e/transformed/jetified-firebase-analytics-22.4.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-analytics:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/308745a438449d9b48372a87eb24c78e/transformed/jetified-firebase-analytics-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/1f72a5eb47c212ee070d2ff64738ad87/transformed/jetified-play-services-measurement-api-22.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/1f72a5eb47c212ee070d2ff64738ad87/transformed/jetified-play-services-measurement-api-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/83739eedc11a599a4b3a3c0dbac2afed/transformed/jetified-firebase-installations-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/83739eedc11a599a4b3a3c0dbac2afed/transformed/jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/d88fa6398fa85124a667a5da3630b12b/transformed/jetified-firebase-common-ktx-21.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/d88fa6398fa85124a667a5da3630b12b/transformed/jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/4d85274071a63f1245fabfe34a7c8499/transformed/jetified-firebase-common-21.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/4d85274071a63f1245fabfe34a7c8499/transformed/jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/b31d8edc373bef4dd06e5a23de0b36c9/transformed/jetified-activity-1.8.1/jars/classes.jar"
      resolved="androidx.activity:activity:1.8.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/b31d8edc373bef4dd06e5a23de0b36c9/transformed/jetified-activity-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:device_info_plus::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/8ba5cdcda7ade9fe94c78f492e8c2e1a/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/8ba5cdcda7ade9fe94c78f492e8c2e1a/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.device_info:device_info_plus:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/device_info_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/8ba5cdcda7ade9fe94c78f492e8c2e1a/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:file_picker::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/66f86c0a96da12dba5868328d61cd550/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/66f86c0a96da12dba5868328d61cd550/transformed/out/jars/libs/R.jar"
      resolved="com.mr.flutter.plugin.filepicker:file_picker:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/file_picker/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/66f86c0a96da12dba5868328d61cd550/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_auth::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/9c2afb0b813bcafc724c5e94a9c037aa/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/9c2afb0b813bcafc724c5e94a9c037aa/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.firebase.auth:firebase_auth:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/firebase_auth/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/9c2afb0b813bcafc724c5e94a9c037aa/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_messaging::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/941e2ed805dac426bf3690166fa0e7a6/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/941e2ed805dac426bf3690166fa0e7a6/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.firebasemessaging:firebase_messaging:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/firebase_messaging/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/941e2ed805dac426bf3690166fa0e7a6/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_core::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/86fdd59950f4295553d6a87f85f6b5df/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/86fdd59950f4295553d6a87f85f6b5df/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.firebase.core:firebase_core:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/firebase_core/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/86fdd59950f4295553d6a87f85f6b5df/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_local_notifications::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/53c4db14e314841338db0c16ee393fe9/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/53c4db14e314841338db0c16ee393fe9/transformed/out/jars/libs/R.jar"
      resolved="com.dexterous.flutterlocalnotifications:flutter_local_notifications:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/flutter_local_notifications/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/53c4db14e314841338db0c16ee393fe9/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/be754d313e32bd1842d066de247cfe37/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/be754d313e32bd1842d066de247cfe37/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle:1.0"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/flutter_plugin_android_lifecycle/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/be754d313e32bd1842d066de247cfe37/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:google_sign_in_android::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/2a5ec2a734a0879ceaf856ca7a7d9260/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/2a5ec2a734a0879ceaf856ca7a7d9260/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.googlesignin:google_sign_in_android:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/google_sign_in_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/2a5ec2a734a0879ceaf856ca7a7d9260/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:path_provider_android::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/cf7fb8cb5a86776f334175cb7961f4fb/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/cf7fb8cb5a86776f334175cb7961f4fb/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.pathprovider:path_provider_android:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/path_provider_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/cf7fb8cb5a86776f334175cb7961f4fb/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:share_plus::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/d5e100015b3855567cf61a91f9e039b0/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/d5e100015b3855567cf61a91f9e039b0/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.share:share_plus:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/share_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/d5e100015b3855567cf61a91f9e039b0/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:url_launcher_android::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0c5b04c7bf980fb05c4b026c8a48bb71/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/0c5b04c7bf980fb05c4b026c8a48bb71/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.urllauncher:url_launcher_android:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/url_launcher_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0c5b04c7bf980fb05c4b026c8a48bb71/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:video_player_android::release"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/1a8324f94777a3fa625815492086d2a8/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.10.2/transforms/1a8324f94777a3fa625815492086d2a8/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.videoplayer:video_player_android:1.0-SNAPSHOT"
      partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/video_player_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/1a8324f94777a3fa625815492086d2a8/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/flutter_embedding_release/1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095/2bdae67d5f76761e92d689bcb16ea23d38879ab0/flutter_embedding_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/42b3f2626c38cee65611acf1e6077550/transformed/browser-1.8.0/jars/classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/42b3f2626c38cee65611acf1e6077550/transformed/browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c51032aab0455ec49a66ad7dca627621/transformed/jetified-core-ktx-1.13.1/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c51032aab0455ec49a66ad7dca627621/transformed/jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/5e6321b8f6eb0a1304a4b169e99742a2/transformed/jetified-play-services-measurement-22.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/5e6321b8f6eb0a1304a4b169e99742a2/transformed/jetified-play-services-measurement-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/4f5d2cc97c95459050bdb4c821b9ce6c/transformed/jetified-play-services-measurement-sdk-22.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/4f5d2cc97c95459050bdb4c821b9ce6c/transformed/jetified-play-services-measurement-sdk-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c664e13cfdd47c6834e52927c949f79d/transformed/jetified-play-services-measurement-impl-22.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c664e13cfdd47c6834e52927c949f79d/transformed/jetified-play-services-measurement-impl-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/a40b6b63cd39f270223c292dae302b48/transformed/jetified-play-services-stats-17.0.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/a40b6b63cd39f270223c292dae302b48/transformed/jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/2a6ea2b497bb835219065d27777052a1/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/2a6ea2b497bb835219065d27777052a1/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/f36b07326e0eac07de55ea5bc72968e2/transformed/loader-1.1.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/f36b07326e0eac07de55ea5bc72968e2/transformed/loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/1114c23e2c816ebcdc0309866a526250/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/1114c23e2c816ebcdc0309866a526250/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/eed07d29d649392bff914601e061d99e/transformed/jetified-play-services-base-18.5.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/eed07d29d649392bff914601e061d99e/transformed/jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/fe83a4913b800395177abd1033494a2f/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/fe83a4913b800395177abd1033494a2f/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c4c687aa7f0a6ba70ccd03af3a086997/transformed/core-1.13.1/jars/classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c4c687aa7f0a6ba70ccd03af3a086997/transformed/core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/ed087e69d6bcd9f9cdb02111889f263a/transformed/jetified-lifecycle-process-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/ed087e69d6bcd9f9cdb02111889f263a/transformed/jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-java8/2.7.0/2ad14aed781c4a73ed4dbb421966d408a0a06686/lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.7.0/85334205d65cca70ed0109c3acbd29e22a2d9cb1/lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/200d3ecd9095f0fe1d9c83cc86f30269/transformed/lifecycle-viewmodel-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/200d3ecd9095f0fe1d9c83cc86f30269/transformed/lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/54c30ac3f9320cfd4e16651caf1f1dc3/transformed/lifecycle-runtime-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/54c30ac3f9320cfd4e16651caf1f1dc3/transformed/lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/f82d61a83dc3eb6ddaffb9a18b4e388d/transformed/lifecycle-livedata-core-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/f82d61a83dc3eb6ddaffb9a18b4e388d/transformed/lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/5100cf103de25be1759e7373415348d3/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/5100cf103de25be1759e7373415348d3/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/79ebe062ef5163e8e7ca3a4b7f5b8fc7/transformed/jetified-window-java-1.2.0/jars/classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/79ebe062ef5163e8e7ca3a4b7f5b8fc7/transformed/jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/bd64efd7412b2dd2150e0a587d4bf3dd/transformed/jetified-window-1.2.0/jars/classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/bd64efd7412b2dd2150e0a587d4bf3dd/transformed/jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.7.3/38d9cad3a0b03a10453b56577984bdeb48edeed5/kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/7a5c9cd3d61303acefe491142c28bc81/transformed/jetified-ads-adservices-java-1.1.0-beta11/jars/classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/7a5c9cd3d61303acefe491142c28bc81/transformed/jetified-ads-adservices-java-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/dbd3230a664ab85fe1b4b2804144ddc0/transformed/jetified-ads-adservices-1.1.0-beta11/jars/classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/dbd3230a664ab85fe1b4b2804144ddc0/transformed/jetified-ads-adservices-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.3/2b09627576f0989a436a00a4a54b55fa5026fb86/kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-play-services/1.7.3/7087d47913cfb0062c9909dacbfc78fe44c5ecff/kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/3487c1108e7eca08048ef88a03cedc75/transformed/jetified-firebase-installations-interop-17.1.1/jars/classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/3487c1108e7eca08048ef88a03cedc75/transformed/jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0fd2bd2a76397511090d770fbe1df250/transformed/jetified-play-services-tasks-18.2.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0fd2bd2a76397511090d770fbe1df250/transformed/jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/323dafbd6565d1581ee2442a2e90f93d/transformed/jetified-play-services-ads-identifier-18.0.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/323dafbd6565d1581ee2442a2e90f93d/transformed/jetified-play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/5a8a2f0d3fbaed109aaf676f0ccee4d5/transformed/jetified-play-services-measurement-sdk-api-22.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/5a8a2f0d3fbaed109aaf676f0ccee4d5/transformed/jetified-play-services-measurement-sdk-api-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/684e86c4eed61b0e6cffc51a6d20b4c3/transformed/jetified-play-services-measurement-base-22.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:22.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/684e86c4eed61b0e6cffc51a6d20b4c3/transformed/jetified-play-services-measurement-base-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/79eb76abc1e4de5a04130ff1a8ce7008/transformed/jetified-firebase-measurement-connector-19.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/79eb76abc1e4de5a04130ff1a8ce7008/transformed/jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/972df2849118f2c05be4cf0d045ed9a5/transformed/jetified-play-services-basement-18.5.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.5.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/972df2849118f2c05be4cf0d045ed9a5/transformed/jetified-play-services-basement-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/6c7446af52789e727ec30a776361fc22/transformed/fragment-1.7.1/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/6c7446af52789e727ec30a776361fc22/transformed/fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.22/b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1/kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/a08aa573c37e9bfb0574cb13a2094c19/transformed/jetified-annotation-experimental-1.4.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/a08aa573c37e9bfb0574cb13a2094c19/transformed/jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/314b4d6b44c9a9c44beb64a2feb08fdb/transformed/jetified-savedstate-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/314b4d6b44c9a9c44beb64a2feb08fdb/transformed/jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.8.22/4dabb8248310d833bb6a8b516024a91fd3d275c/kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/9fa44571dfb60bb81cbc6a2d09e09970/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/9fa44571dfb60bb81cbc6a2d09e09970/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.2.0/34dbc21d203cc4d4d623ac572a21acd4ccd716af/collection-1.2.0.jar"
      resolved="androidx.collection:collection:1.2.0"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/2b4d7c5b4e7f86ff2aa71546dd63a280/transformed/localbroadcastmanager-1.1.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/2b4d7c5b4e7f86ff2aa71546dd63a280/transformed/localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/73b27a13d650e39d9613e34242764b15/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/73b27a13d650e39d9613e34242764b15/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/d1b716652c15f371401c05956d5938f6/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/d1b716652c15f371401c05956d5938f6/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.9.1/b17951747e38bf3986a24431b9ba0d039958aa5f/annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/2.0.21/618b539767b4899b4660a83006e052b63f1db551/kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/armeabi_v7a_release/1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095/e0c7d9fa695015a74f8d970cb8e8df87dc35065a/armeabi_v7a_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/arm64_v8a_release/1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095/b8e83e0554ed5a594bd5427861aaec9f3ffd870e/arm64_v8a_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/x86_64_release/1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095/62a3eec18003c50389891f1bf523cf25205bbbb1/x86_64_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:x86_64_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:guava:33.0.0-android@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/33.0.0-android/cfbbdc54f232feedb85746aeeea0722f5244bb9a/guava-33.0.0-android.jar"
      resolved="com.google.guava:guava:33.0.0-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/b421526c5f297295adef1c886e5246c39d4ac629/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/7757f7c235e592725f671b1a8c4a3bba/transformed/jetified-startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/7757f7c235e592725f671b1a8c4a3bba/transformed/jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/8518ddb7bae4e790ed25d9ec9b55b477/transformed/jetified-tracing-1.2.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/8518ddb7bae4e790ed25d9ec9b55b477/transformed/jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.2/c4a06a64e650562f30b7bf9aaec1bfed43aca12b/failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.41.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.checkerframework/checker-qual/3.41.0/8be6df7f1e9bccb19f8f351b3651f0bac2f5e0c/checker-qual-3.41.0.jar"
      resolved="org.checkerframework:checker-qual:3.41.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.36.0/227d4d4957ccc3dc5761bd897e3a0ee587e750a7/error_prone_annotations-2.36.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.36.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/678419e89edfab56801fc89cbd7f14be/transformed/jetified-firebase-components-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/678419e89edfab56801fc89cbd7f14be/transformed/jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-annotations/16.2.0/ba0806703ca285d03fa9c888b5868f101134a501/firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/de9806ab48d4099622010237bc92ad70/transformed/jetified-core-common-2.0.3/jars/classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/de9806ab48d4099622010237bc92ad70/transformed/jetified-core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/dd0afa5a80152bb82469cce5243486c9/transformed/jetified-relinker-1.4.5/jars/classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/dd0afa5a80152bb82469cce5243486c9/transformed/jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.j2objc/j2objc-annotations/2.8/c85270e307e7b822f1086b93689124b89768e273/j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"
      provided="true"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/5339e194bb4c1feaa9879c949a98e4c1/transformed/media-1.1.0/jars/classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/5339e194bb4c1feaa9879c949a98e4c1/transformed/media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0e91df3bfdb9a3199ac625bc57ddd8ea/transformed/preference-1.2.1/jars/classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0e91df3bfdb9a3199ac625bc57ddd8ea/transformed/preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/b9478dd48569c23a389b3507eea92eab/transformed/jetified-media3-extractor-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/b9478dd48569c23a389b3507eea92eab/transformed/jetified-media3-extractor-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/725174b9c7aa476eb436b6b4c81a88f9/transformed/jetified-media3-container-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-container:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/725174b9c7aa476eb436b6b4c81a88f9/transformed/jetified-media3-container-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/73fbf352d82ce9eb26b426cc01571260/transformed/jetified-media3-datasource-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/73fbf352d82ce9eb26b426cc01571260/transformed/jetified-media3-datasource-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/27948852e4caf6a141ee2ca78e1e55e9/transformed/jetified-media3-decoder-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/27948852e4caf6a141ee2ca78e1e55e9/transformed/jetified-media3-decoder-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/3ae842e87d045161e51eb3ee3e12ad8c/transformed/jetified-media3-database-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-database:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/3ae842e87d045161e51eb3ee3e12ad8c/transformed/jetified-media3-database-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/be73f5ea4d8400ac3054251bbc96216a/transformed/jetified-media3-common-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-common:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/be73f5ea4d8400ac3054251bbc96216a/transformed/jetified-media3-common-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/f8b03743d1e6af2e1184c84c352c40fc/transformed/jetified-media3-exoplayer-hls-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-hls:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/f8b03743d1e6af2e1184c84c352c40fc/transformed/jetified-media3-exoplayer-hls-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/f738c1f8e74550be3a1ebcbf440fef0a/transformed/jetified-media3-exoplayer-dash-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-dash:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/f738c1f8e74550be3a1ebcbf440fef0a/transformed/jetified-media3-exoplayer-dash-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-rtsp:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/6c24cb6c2d383147beaf3b1f22786b44/transformed/jetified-media3-exoplayer-rtsp-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-rtsp:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/6c24cb6c2d383147beaf3b1f22786b44/transformed/jetified-media3-exoplayer-rtsp-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0ed21539ff8a568b288fbc861def4b2a/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0ed21539ff8a568b288fbc861def4b2a/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/3bc616e52eed5b34ea25c18d038ccb4a/transformed/jetified-media3-exoplayer-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/3bc616e52eed5b34ea25c18d038ccb4a/transformed/jetified-media3-exoplayer-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/be384aeb1c73c2ddbea0f40925fc978e/transformed/recyclerview-1.0.0/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/be384aeb1c73c2ddbea0f40925fc978e/transformed/recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/2ce2a59b61bbe5a27bcd8b424c445493/transformed/legacy-support-core-ui-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/2ce2a59b61bbe5a27bcd8b424c445493/transformed/legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c958777f2a157f4eb506edd082bac955/transformed/slidingpanelayout-1.2.0/jars/classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c958777f2a157f4eb506edd082bac955/transformed/slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/b50206e73ac695f1ade103eedc872b98/transformed/jetified-play-services-auth-21.0.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/b50206e73ac695f1ade103eedc872b98/transformed/jetified-play-services-auth-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/c85df6dcbd586cbcf1d567e0ea85d24b/transformed/appcompat-1.1.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/c85df6dcbd586cbcf1d567e0ea85d24b/transformed/appcompat-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/d74590b748e9f7698f813789122cf2ec/transformed/jetified-firebase-messaging-24.1.1/jars/classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.1.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/d74590b748e9f7698f813789122cf2ec/transformed/jetified-firebase-messaging-24.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/3028159452d803ddf153d6f06694b3fb/transformed/jetified-play-services-auth-base-18.0.10/jars/classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/3028159452d803ddf153d6f06694b3fb/transformed/jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/909795db9f48caaf265cfaed8ad8246b/transformed/jetified-play-services-fido-20.1.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/909795db9f48caaf265cfaed8ad8246b/transformed/jetified-play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/1652928cfe004331c078aa1786ceb9ef/transformed/jetified-fragment-ktx-1.7.1/jars/classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/1652928cfe004331c078aa1786ceb9ef/transformed/jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/6469220c36212a7b1901c99ab573529b/transformed/jetified-activity-ktx-1.8.1/jars/classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/6469220c36212a7b1901c99ab573529b/transformed/jetified-activity-ktx-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/f1192aea7378e44067cd6b39f2a7f99c/transformed/jetified-lifecycle-runtime-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/f1192aea7378e44067cd6b39f2a7f99c/transformed/jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/6819e42229d110814b3e4979b917d8ee/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/6819e42229d110814b3e4979b917d8ee/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/fc9fbdf6d3635bc5e905e2992079499a/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/fc9fbdf6d3635bc5e905e2992079499a/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0bb928ac07569d283505dca2193c0d0c/transformed/jetified-savedstate-ktx-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0bb928ac07569d283505dca2193c0d0c/transformed/jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.datastore/datastore-preferences-external-protobuf/1.1.3/ddd0a2d64e3c928359c993d1291e535d5d7fc9a3/datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.datastore/datastore-preferences-proto/1.1.3/6d7430ed8d2b5f2b8675dad8d196ba5dd710921b/datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.datastore/datastore-preferences-core-jvm/1.1.3/fb991f11389ccf2a5d5d4c99783ff958bc400/datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.datastore/datastore-core-okio-jvm/1.1.3/ce08f132812044a9778547b299fd812e34dbd602/datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/3f5c810391bbac886cb6e5cebf42a89c/transformed/jetified-datastore-core-release/jars/classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/3f5c810391bbac886cb6e5cebf42a89c/transformed/jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/0df86ce4011cb2c47588189d8c19c0a0/transformed/jetified-datastore-preferences-release/jars/classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/0df86ce4011cb2c47588189d8c19c0a0/transformed/jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/b0ed7d3b548a353cfe053b120fa28626/transformed/jetified-datastore-release/jars/classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/b0ed7d3b548a353cfe053b120fa28626/transformed/jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/345ada6486d2e576b4d5ed63aa411b36/transformed/jetified-firebase-iid-interop-17.1.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/345ada6486d2e576b4d5ed63aa411b36/transformed/jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/257dcfb4f59e713b004b4dfd61307cce/transformed/jetified-play-services-cloud-messaging-17.2.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/257dcfb4f59e713b004b4dfd61307cce/transformed/jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/4b1498c88c5106c218d83baa50a47e19/transformed/jetified-appcompat-resources-1.1.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/4b1498c88c5106c218d83baa50a47e19/transformed/jetified-appcompat-resources-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/53a67a92ed9248e2c44cfcaa6cc32b99/transformed/drawerlayout-1.0.0/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/53a67a92ed9248e2c44cfcaa6cc32b99/transformed/drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/f6ef48e300da36d757905ae895978587/transformed/coordinatorlayout-1.0.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/f6ef48e300da36d757905ae895978587/transformed/coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/689279ad7a7f13ea4db9dacc99379905/transformed/transition-1.4.1/jars/classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/689279ad7a7f13ea4db9dacc99379905/transformed/transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/cea0bdc00618cf7cf93dc7477f0a5520/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/cea0bdc00618cf7cf93dc7477f0a5520/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/2bd050c086fb0abcd76453f1b93466e7/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/2bd050c086fb0abcd76453f1b93466e7/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/aa87e6d877f70b4a84dc2cbac282a417/transformed/swiperefreshlayout-1.0.0/jars/classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/aa87e6d877f70b4a84dc2cbac282a417/transformed/swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/cfe2296a449ca78329ce8c7a82157523/transformed/asynclayoutinflater-1.0.0/jars/classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/cfe2296a449ca78329ce8c7a82157523/transformed/asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/e497ef5fe8c6fadd88c4e23a80af7009/transformed/jetified-profileinstaller-1.3.1/jars/classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/e497ef5fe8c6fadd88c4e23a80af7009/transformed/jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/819b6f49f2f53371b8418d01a24bf8d7/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/819b6f49f2f53371b8418d01a24bf8d7/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection-ktx/1.1.0/f807b2f366f7b75142a67d2f3c10031065b5168/collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/6c207a60a3b5539eaf48629a95fe69dc/transformed/exifinterface-1.3.6/jars/classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/6c207a60a3b5539eaf48629a95fe69dc/transformed/exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.1.0/50b7fb98350d5f42a4e49704b03278542293ba48/concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/aa75635e87677fbe21dff97df664274e/transformed/core-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/aa75635e87677fbe21dff97df664274e/transformed/core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/e2736856521cb764b122681553726c8d/transformed/jetified-firebase-datatransport-18.2.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/e2736856521cb764b122681553726c8d/transformed/jetified-firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/54ea7d45a312c8942dd54e5b3815d828/transformed/jetified-transport-backend-cct-3.1.9/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/54ea7d45a312c8942dd54e5b3815d828/transformed/jetified-transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/5568dcfad8e82a6a96ef2a356b87ac40/transformed/jetified-firebase-encoders-json-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/5568dcfad8e82a6a96ef2a356b87ac40/transformed/jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/7284541f768a7eaea26dd11a8f5b4d0f/transformed/jetified-transport-runtime-3.1.9/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/7284541f768a7eaea26dd11a8f5b4d0f/transformed/jetified-transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-proto/16.0.0/a42d5fd83b96ae7b73a8617d29c94703e18c9992/firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/29810ca18d96a864539898d77089938f/transformed/jetified-transport-api-3.1.0/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/29810ca18d96a864539898d77089938f/transformed/jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders/17.0.0/26f52dc549c42575b155f8c720e84059ee600a85/firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/cf9c290c540e57c17afdaf72e3ee52d1/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/cf9c290c540e57c17afdaf72e3ee52d1/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/e87a1ccf47772e781af021862b33fff6/transformed/jetified-core-1.0.0/jars/classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/e87a1ccf47772e781af021862b33fff6/transformed/jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.10.2/transforms/1f0f3a8ecea8c39c927f041094a2cc31/transformed/jetified-googleid-1.1.0/jars/classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.10.2/transforms/1f0f3a8ecea8c39c927f041094a2cc31/transformed/jetified-googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okio/okio-jvm/3.4.0/4e8bd78a52ab935ce383d0092646922154295e54/okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-parcelize-runtime/1.9.22/de4a21d6560cadd035c69ba3af3ad1afecc95299/kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-android-extensions-runtime/1.9.22/ee3bc0c3b55cb516ac92d6a093e1b939166b86a2/kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="com.google.code.gson:gson:2.12.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.12.0/10596b68aaca6230f7c40bfd9298b21ff4b84103/gson-2.12.0.jar"
      resolved="com.google.code.gson:gson:2.12.0"/>
</libraries>
