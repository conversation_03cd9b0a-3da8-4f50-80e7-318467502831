<variant
    name="release"
    package="com.zerokoin.www"
    minSdkVersion="23"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="/Users/<USER>/Desktop/zero_koin/build/app/intermediates/merged_manifest/release/processReleaseMainManifest/AndroidManifest.xml"
    proguardFiles="/Users/<USER>/Desktop/zero_koin/build/app/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.7.0:/Users/<USER>/development/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro:proguard-rules.pro"
    partialResultsDir="/Users/<USER>/Desktop/zero_koin/build/app/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
    desugaredMethodsFiles="/Users/<USER>/.gradle/caches/8.10.2/transforms/2134c3748541be5955cb244aa8ab45c4/transformed/desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt:/Users/<USER>/.gradle/caches/8.10.2/transforms/677bc88cb1676315edcc02d4feb9e798/transformed/D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="/Users/<USER>/Desktop/zero_koin/build/app/intermediates/javac/release/compileReleaseJavaWithJavac/classes:/Users/<USER>/Desktop/zero_koin/build/app/tmp/kotlin-classes/release:/Users/<USER>/Desktop/zero_koin/build/app/kotlinToolingMetadata:/Users/<USER>/Desktop/zero_koin/build/app/intermediates/compile_and_runtime_not_namespaced_r_class_jar/release/processReleaseResources/R.jar"
      type="MAIN"
      applicationId="com.zerokoin.www"
      generatedSourceFolders="/Users/<USER>/Desktop/zero_koin/build/app/generated/ap_generated_sources/release/out"
      generatedResourceFolders="/Users/<USER>/Desktop/zero_koin/build/app/generated/res/processReleaseGoogleServices:/Users/<USER>/Desktop/zero_koin/build/app/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/8.10.2/transforms/2134c3748541be5955cb244aa8ab45c4/transformed/desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt:/Users/<USER>/.gradle/caches/8.10.2/transforms/677bc88cb1676315edcc02d4feb9e798/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
