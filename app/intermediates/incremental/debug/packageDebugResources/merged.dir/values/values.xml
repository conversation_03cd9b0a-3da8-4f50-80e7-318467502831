<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="ic_launcher_background">#FFFFFF</color>
    <color name="notification_color">#0682A2</color>
    <string name="default_web_client_id" translatable="false">898085251418-kuc3sj6fa5t2lgjgfddet3d9jsud9796.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">898085251418</string>
    <string name="google_api_key" translatable="false">AIzaSyDyMWE4ueqdGT7FSWOFM-2VfmlLHduZQIQ</string>
    <string name="google_app_id" translatable="false">1:898085251418:android:e5d9e4144565ea7e1f2b63</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDyMWE4ueqdGT7FSWOFM-2VfmlLHduZQIQ</string>
    <string name="google_storage_bucket" translatable="false">zerokoin-4e239.firebasestorage.app</string>
    <string name="project_id" translatable="false">zerokoin-4e239</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>