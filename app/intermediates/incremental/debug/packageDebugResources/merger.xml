<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res"><file name="ic_launcher" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/values-night/styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style></file><file path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/values/colors.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color><color name="notification_color">#0682A2</color></file><file path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/values/styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style></file><file name="ic_stat_notificationlogo" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-xhdpi/ic_stat_notificationlogo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-xhdpi/ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_notificationlogo" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-xxhdpi/ic_stat_notificationlogo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-xxhdpi/ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_notificationlogo" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-hdpi/ic_stat_notificationlogo.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-hdpi/ic_launcher_foreground.png" qualifiers="hdpi-v4" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-v21/launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_stat_notificationlogo" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-mdpi/ic_stat_notificationlogo.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-mdpi/ic_launcher_foreground.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_stat_notificationlogo" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-xxxhdpi/ic_stat_notificationlogo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/drawable-xxxhdpi/ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/zero_koin/android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/zero_koin/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/zero_koin/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/zero_koin/build/app/generated/res/resValues/debug"/><source path="/Users/<USER>/Desktop/zero_koin/build/app/generated/res/processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/zero_koin/build/app/generated/res/resValues/debug"/><source path="/Users/<USER>/Desktop/zero_koin/build/app/generated/res/processDebugGoogleServices"><file path="/Users/<USER>/Desktop/zero_koin/build/app/generated/res/processDebugGoogleServices/values/values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">898085251418-kuc3sj6fa5t2lgjgfddet3d9jsud9796.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">898085251418</string><string name="google_api_key" translatable="false">AIzaSyDyMWE4ueqdGT7FSWOFM-2VfmlLHduZQIQ</string><string name="google_app_id" translatable="false">1:898085251418:android:e5d9e4144565ea7e1f2b63</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDyMWE4ueqdGT7FSWOFM-2VfmlLHduZQIQ</string><string name="google_storage_bucket" translatable="false">zerokoin-4e239.firebasestorage.app</string><string name="project_id" translatable="false">zerokoin-4e239</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>