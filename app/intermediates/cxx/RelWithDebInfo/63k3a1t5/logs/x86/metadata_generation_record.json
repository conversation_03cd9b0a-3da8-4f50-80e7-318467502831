[{"level_": 0, "message_": "Start JSON generation. Platform version: 23 min SDK version: x86", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Desktop/zero_koin/build/.cxx/RelWithDebInfo/63k3a1t5/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]