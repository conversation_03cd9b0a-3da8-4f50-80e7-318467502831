/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=23 \
  -DANDROID_PLATFORM=android-23 \
  -DANDROID_ABI=x86_64 \
  -DCMAKE_ANDROID_ARCH_ABI=x86_64 \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/zero_koin/build/app/intermediates/cxx/RelWithDebInfo/63k3a1t5/obj/x86_64 \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/zero_koin/build/app/intermediates/cxx/RelWithDebInfo/63k3a1t5/obj/x86_64 \
  -DCMAKE_BUILD_TYPE=RelWithDebInfo \
  -B/Users/<USER>/Desktop/zero_koin/build/.cxx/RelWithDebInfo/63k3a1t5/x86_64 \
  -GNinja \
  -Wno-dev \
  --no-warn-unused-cli
