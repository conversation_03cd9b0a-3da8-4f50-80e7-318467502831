<svg width="55" height="56" viewBox="0 0 55 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="54.5008" height="54.5008" rx="16.3502" fill="url(#paint0_linear_2016_913)"/>
<g opacity="0.25" filter="url(#filter0_f_2016_913)">
<rect x="8.21094" y="27.25" width="37.1272" height="23.2045" rx="11.6022" fill="url(#paint1_linear_2016_913)"/>
</g>
<rect x="7.08521" y="7.08496" width="40.2211" height="40.2211" rx="16.3502" fill="url(#paint2_linear_2016_913)"/>
<g clip-path="url(#clip0_2016_913)" filter="url(#filter1_i_2016_913)">
<g filter="url(#filter2_i_2016_913)">
<path d="M32.6128 28.7479L33.2848 24.41H29.145V21.5904C29.145 20.4042 29.7229 19.2452 31.5711 19.2452H33.4797V15.5512C32.3682 15.3712 31.2451 15.2737 30.1195 15.2598C26.7121 15.2598 24.4876 17.3406 24.4876 21.1023V24.41H20.7107V28.7479H24.4876V39.2401H29.145V28.7479H32.6128Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_2016_913" x="2.75094" y="21.79" width="48.0472" height="34.1241" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.73" result="effect1_foregroundBlur_2016_913"/>
</filter>
<filter id="filter1_i_2016_913" x="14.7153" y="14.7148" width="24.5254" height="24.7979" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.272504"/>
<feGaussianBlur stdDeviation="0.68126"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2016_913"/>
</filter>
<filter id="filter2_i_2016_913" x="20.7107" y="15.2598" width="12.769" height="24.253" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.272504"/>
<feGaussianBlur stdDeviation="0.545008"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2016_913"/>
</filter>
<linearGradient id="paint0_linear_2016_913" x1="4.5814" y1="3.56992" x2="45.0405" y2="54.5008" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBFBFC"/>
<stop offset="1" stop-color="#DBDDE8"/>
</linearGradient>
<linearGradient id="paint1_linear_2016_913" x1="26.7745" y1="49.7765" x2="26.7745" y2="27.25" gradientUnits="userSpaceOnUse">
<stop stop-color="#0062E0"/>
<stop offset="1" stop-color="#19AFFF"/>
</linearGradient>
<linearGradient id="paint2_linear_2016_913" x1="27.1958" y1="46.1309" x2="27.1958" y2="7.08496" gradientUnits="userSpaceOnUse">
<stop stop-color="#0062E0"/>
<stop offset="1" stop-color="#19AFFF"/>
</linearGradient>
<clipPath id="clip0_2016_913">
<rect width="24.5254" height="24.5254" fill="white" transform="translate(14.7153 14.7148)"/>
</clipPath>
</defs>
</svg>
